package food

import (
	"errors"
	"gorm.io/gorm"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/pkg/errs"
)

// IFoodCategoryRepo 定义了食物分类仓库需要实现的所有方法
type IFoodCategoryRepo interface {
	Create(category *entities.FoodCategory) error
	GetByID(id int) (*entities.FoodCategory, error)
	GetByName(name string) (*entities.FoodCategory, error)
	Update(category *entities.FoodCategory) error
	Delete(id int) error
	List(offset, limit int) ([]*entities.FoodCategory, int64, error)
	GetAll() ([]*entities.FoodCategory, error)
	GetAllOrderBySortOrder() ([]*entities.FoodCategory, error)
	ExistsByName(name string) (bool, error)
	ExistsByNameExcludeID(name string, excludeID int) (bool, error)
	Search(keyword string, offset, limit int) ([]*entities.FoodCategory, int64, error)
	GetMaxSortOrder() (int, error)
	UpdateSortOrder(id int, sortOrder int) error
}

// foodCategoryRepository 食物分类仓储
type foodCategoryRepository struct {
	db *gorm.DB
}

// NewFoodCategoryRepository 创建食物分类仓储实例
func NewFoodCategoryRepository(db *gorm.DB) IFoodCategoryRepo {
	return &foodCategoryRepository{
		db: db,
	}
}

// 确保 foodCategoryRepository 实现了 IFoodCategoryRepo 接口
var _ IFoodCategoryRepo = &foodCategoryRepository{}

// Create 创建新食物分类
func (r *foodCategoryRepository) Create(category *entities.FoodCategory) error {
	if err := r.db.Create(category).Error; err != nil {
		// 检查是否是重复键错误
		if repositories.IsDuplicateKeyError(err) {
			if repositories.ContainsField(err.Error(), "name") || repositories.ContainsField(err.Error(), "uk_category_name") {
				return errs.New(errs.CodeFoodAlreadyExists, "food category name already exists")
			}
		}
		return errs.Wrap(errs.CodeDatabaseError, "failed to create food category", err)
	}
	return nil
}

// GetByID 根据ID获取食物分类
func (r *foodCategoryRepository) GetByID(id int) (*entities.FoodCategory, error) {
	var category entities.FoodCategory
	if err := r.db.Where("id = ?", id).First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(errs.CodeFoodCategoryNotFound, "food category not found")
		}
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to query food category by id", err)
	}
	return &category, nil
}

// GetByName 根据名称获取食物分类
func (r *foodCategoryRepository) GetByName(name string) (*entities.FoodCategory, error) {
	var category entities.FoodCategory
	if err := r.db.Where("name = ?", name).First(&category).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(errs.CodeFoodCategoryNotFound, "food category not found")
		}
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to query food category by name", err)
	}
	return &category, nil
}

// Update 更新食物分类信息
func (r *foodCategoryRepository) Update(category *entities.FoodCategory) error {
	if err := r.db.Save(category).Error; err != nil {
		// 检查是否是重复键错误
		if repositories.IsDuplicateKeyError(err) {
			if repositories.ContainsField(err.Error(), "name") || repositories.ContainsField(err.Error(), "uk_category_name") {
				return errs.New(errs.CodeFoodAlreadyExists, "food category name already exists")
			}
		}
		return errs.Wrap(errs.CodeDatabaseError, "failed to update food category", err)
	}
	return nil
}

// Delete 删除食物分类
func (r *foodCategoryRepository) Delete(id int) error {
	if err := r.db.Delete(&entities.FoodCategory{}, id).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to delete food category", err)
	}
	return nil
}

// List 获取食物分类列表（分页）
func (r *foodCategoryRepository) List(offset, limit int) ([]*entities.FoodCategory, int64, error) {
	var categories []*entities.FoodCategory
	var total int64

	// 获取总数
	if err := r.db.Model(&entities.FoodCategory{}).Count(&total).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to count food categories", err)
	}

	// 获取分页数据，按排序顺序和ID排序
	if err := r.db.Offset(offset).Limit(limit).Order("sort_order ASC, id ASC").Find(&categories).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to list food categories", err)
	}

	return categories, total, nil
}

// GetAll 获取所有食物分类
func (r *foodCategoryRepository) GetAll() ([]*entities.FoodCategory, error) {
	var categories []*entities.FoodCategory
	if err := r.db.Order("sort_order ASC, id ASC").Find(&categories).Error; err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to get all food categories", err)
	}
	return categories, nil
}

// GetAllOrderBySortOrder 获取所有食物分类（按排序顺序）
func (r *foodCategoryRepository) GetAllOrderBySortOrder() ([]*entities.FoodCategory, error) {
	var categories []*entities.FoodCategory
	if err := r.db.Order("sort_order ASC, id ASC").Find(&categories).Error; err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to get all food categories order by sort order", err)
	}
	return categories, nil
}

// ExistsByName 检查分类名称是否已存在
func (r *foodCategoryRepository) ExistsByName(name string) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.FoodCategory{}).Where("name = ?", name).Count(&count).Error; err != nil {
		return false, errs.Wrap(errs.CodeDatabaseError, "failed to check food category name exists", err)
	}
	return count > 0, nil
}

// ExistsByNameExcludeID 检查分类名称是否已存在（排除指定ID）
func (r *foodCategoryRepository) ExistsByNameExcludeID(name string, excludeID int) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.FoodCategory{}).Where("name = ? AND id != ?", name, excludeID).Count(&count).Error; err != nil {
		return false, errs.Wrap(errs.CodeDatabaseError, "failed to check food category name exists exclude id", err)
	}
	return count > 0, nil
}

// Search 搜索食物分类
func (r *foodCategoryRepository) Search(keyword string, offset, limit int) ([]*entities.FoodCategory, int64, error) {
	var categories []*entities.FoodCategory
	var total int64

	query := r.db.Model(&entities.FoodCategory{})

	// 添加关键词搜索条件
	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to count search food categories", err)
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("sort_order ASC, id ASC").Find(&categories).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to search food categories", err)
	}

	return categories, total, nil
}

// GetMaxSortOrder 获取最大排序顺序
func (r *foodCategoryRepository) GetMaxSortOrder() (int, error) {
	var maxSortOrder int
	if err := r.db.Model(&entities.FoodCategory{}).Select("COALESCE(MAX(sort_order), 0)").Scan(&maxSortOrder).Error; err != nil {
		return 0, errs.Wrap(errs.CodeDatabaseError, "failed to get max sort order", err)
	}
	return maxSortOrder, nil
}

// UpdateSortOrder 更新排序顺序
func (r *foodCategoryRepository) UpdateSortOrder(id int, sortOrder int) error {
	if err := r.db.Model(&entities.FoodCategory{}).Where("id = ?", id).Update("sort_order", sortOrder).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to update food category sort order", err)
	}
	return nil
}
