package middleware

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/pkg/errs"
	"strings"
)

// ErrorHandler 是一个全局错误处理中间件
// 统一处理所有错误，优先处理 CodeError，其他错误作为内部错误处理
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 在所有请求处理完毕后，检查是否有错误
		if len(c.Errors) == 0 {
			// 检查路由是否存在
			if c.Writer.Status() == http.StatusNotFound {
				response.Error(c, consts.CodeNotFound)
			}
			return
		}

		// 只处理最新的一个错误
		err := c.Errors.Last().Err

		// 优先处理统一的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			handleCodeError(c, codeErr)
			return
		}

		// 记录错误日志（包含请求上下文）
		log.Printf("Request error [%s %s]: %v", c.Request.Method, c.Request.URL.Path, err)

		// 对于参数绑定等错误
		if c.Writer.Status() == http.StatusBadRequest || c.Writer.Status() == 0 {
			log.Printf("Parameter or client error: %v", err)
			response.Error(c, consts.CodeParameterError)
			return
		}

		// 未知的服务器内部错误
		log.Printf("Internal server error: %v", err)
		response.Error(c, consts.CodeInternalError)
	}
}

// handleCodeError 处理统一的 CodeError
func handleCodeError(c *gin.Context, codeErr *errs.CodeError) {
	// 记录完整的错误链路
	errorChain := buildErrorChain(codeErr)
	log.Printf("Error Chain [%s %s]: %s",
		c.Request.Method, c.Request.URL.Path, errorChain)

	// 获取映射配置
	mapping := errs.GetErrorMapping(codeErr.Code)

	// 返回脱敏的错误信息给前端
	response.ErrorWithCodeAndMessage(c, mapping.HTTPStatus, codeErr.Code, mapping.Message)
}

// buildErrorChain 构建完整的错误链路字符串
func buildErrorChain(err error) string {
	var chain []string
	current := err

	for current != nil {
		// 如果是 CodeError，记录其业务信息
		var codeErr *errs.CodeError
		if errors.As(current, &codeErr) {
			chain = append(chain, fmt.Sprintf("[%s: %s]", codeErr.Code, codeErr.Message))
			current = codeErr.Err
		} else {
			// 普通错误，直接记录
			chain = append(chain, fmt.Sprintf("[%v]", current))
			// 尝试展开
			current = errors.Unwrap(current)
		}
	}

	return strings.Join(chain, " -> ")
}
