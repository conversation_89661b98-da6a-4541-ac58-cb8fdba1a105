package nutrition

import (
	"context"
	"errors"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/repositories/nutrition"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/repositories"
	"shikeyinxiang/internal/service"
)

// nutritionAdviceLogic 营养建议业务逻辑实现
type nutritionAdviceLogic struct {
	nutritionAdviceRepo  nutrition.INutritionAdviceRepo
	nutritionStatService service.INutritionStatService
}

// NewNutritionAdviceLogic 创建营养建议业务逻辑实例
func NewNutritionAdviceLogic(
	nutritionAdviceRepo nutrition.INutritionAdviceRepo,
	nutritionStatService service.INutritionStatService,
) service.INutritionAdviceService {
	return &nutritionAdviceLogic{
		nutritionAdviceRepo:  nutritionAdviceRepo,
		nutritionStatService: nutritionStatService,
	}
}

// 确保 nutritionAdviceLogic 实现了 INutritionAdviceService 接口
var _ service.INutritionAdviceService = &nutritionAdviceLogic{}

// GetNutritionAdvice 根据用户营养统计获取个性化建议
func (n *nutritionAdviceLogic) GetNutritionAdvice(ctx context.Context, userID int64, date string) ([]*v1.NutritionAdviceDisplayResponse, error) {
	if userID <= 0 {
		return nil, errs.New(errs.CodeNutritionInvalidParams, "userID must be positive")
	}

	// 获取当日营养摄入统计
	nutritionStat, err := n.nutritionStatService.GetDailyNutritionStat(ctx, userID, date)
	if err != nil {
		return nil, err // 透传错误
	}

	var adviceList []*v1.NutritionAdviceDisplayResponse

	// 根据与目标的差距生成建议

	// 检查蛋白质摄入
	if proteinAdvice, err := n.GetAdviceByCondition(ctx, "protein", int(nutritionStat.ProteinPercentage)); err == nil && proteinAdvice != nil {
		adviceList = append(adviceList, n.convertToDisplayResponse(proteinAdvice))
	}

	// 检查碳水化合物摄入
	if carbsAdvice, err := n.GetAdviceByCondition(ctx, "carbs", int(nutritionStat.CarbsPercentage)); err == nil && carbsAdvice != nil {
		adviceList = append(adviceList, n.convertToDisplayResponse(carbsAdvice))
	}

	// 检查脂肪摄入
	if fatAdvice, err := n.GetAdviceByCondition(ctx, "fat", int(nutritionStat.FatPercentage)); err == nil && fatAdvice != nil {
		adviceList = append(adviceList, n.convertToDisplayResponse(fatAdvice))
	}

	// 检查热量摄入
	if calorieAdvice, err := n.GetAdviceByCondition(ctx, "calorie", int(nutritionStat.CaloriePercentage)); err == nil && calorieAdvice != nil {
		adviceList = append(adviceList, n.convertToDisplayResponse(calorieAdvice))
	}

	// 如果没有任何建议，添加一个默认建议
	if len(adviceList) == 0 {
		if defaultAdvice, err := n.GetDefaultAdvice(ctx); err == nil && defaultAdvice != nil {
			adviceList = append(adviceList, n.convertToDisplayResponse(defaultAdvice))
		} else {
			// 如果数据库中没有默认建议，使用硬编码的默认建议
			adviceList = append(adviceList, &v1.NutritionAdviceDisplayResponse{
				Type:        "info",
				Title:       "营养摄入基本合理",
				Description: "今日的营养摄入基本合理，保持均衡饮食有助于健康。",
			})
		}
	}

	return adviceList, nil
}

// GetAdviceByCondition 根据条件类型和百分比匹配建议
func (n *nutritionAdviceLogic) GetAdviceByCondition(ctx context.Context, conditionType string, percentage int) (*v1.NutritionAdviceResponse, error) {
	if conditionType == "" {
		return nil, errs.New(errs.CodeNutritionInvalidParams, "conditionType is required")
	}

	advices, err := n.nutritionAdviceRepo.FindByConditionTypeAndPercentage(conditionType, percentage)
	if err != nil {
		return nil, err // 透传 Repository 错误
	}

	if len(advices) == 0 {
		return nil, nil // 没有匹配的建议
	}

	// 返回优先级最高的建议（已按优先级降序排列）
	return n.convertToResponse(advices[0]), nil
}

// GetDefaultAdvice 获取默认建议
func (n *nutritionAdviceLogic) GetDefaultAdvice(ctx context.Context) (*v1.NutritionAdviceResponse, error) {
	advice, err := n.nutritionAdviceRepo.FindDefaultAdvice()
	if err != nil {
		var notFound *repositories.NotFoundError
		if errors.As(err, &notFound) {
			return nil, nil // 没有默认建议
		}
		return nil, err // 透传 Repository 错误
	}

	return n.convertToResponse(advice), nil
}

// CreateNutritionAdvice 创建营养建议（管理功能）
func (n *nutritionAdviceLogic) CreateNutritionAdvice(ctx context.Context, req *v1.NutritionAdviceManageReq) (*v1.NutritionAdviceResponse, error) {
	if req == nil {
		return nil, errs.New(errs.CodeNutritionInvalidParams, "request is required")
	}

	// 检查标题是否已存在
	exists, err := n.nutritionAdviceRepo.ExistsByTitle(req.Title)
	if err != nil {
		return nil, err // 透传 Repository 错误
	}
	if exists {
		return nil, errs.New(errs.CodeNutritionAlreadyExists, "nutrition advice title already exists")
	}

	// 创建实体
	advice := entities.NewNutritionAdvice(req.Type, req.Title, req.Description, req.ConditionType, req.Priority)
	advice.SetPercentageRange(req.MinPercentage, req.MaxPercentage)
	advice.SetAsDefault(req.IsDefault)
	advice.SetStatus(req.Status)

	// 保存到数据库
	if err := n.nutritionAdviceRepo.Create(advice); err != nil {
		return nil, err // 透传 Repository 错误
	}

	return n.convertToResponse(advice), nil
}

// UpdateNutritionAdvice 更新营养建议
func (n *nutritionAdviceLogic) UpdateNutritionAdvice(ctx context.Context, id int64, req *v1.NutritionAdviceUpdateReq) (*v1.NutritionAdviceResponse, error) {
	if id <= 0 {
		return nil, errs.New(errs.CodeNutritionInvalidParams, "id must be positive")
	}
	if req == nil {
		return nil, errs.New(errs.CodeNutritionInvalidParams, "request is required")
	}

	// 获取现有建议
	advice, err := n.nutritionAdviceRepo.GetByID(id)
	if err != nil {
		var notFound *repositories.NotFoundError
		if errors.As(err, &notFound) {
			return nil, errs.New(errs.CodeNutritionNotFound, "nutrition advice not found")
		}
		return nil, err // 透传 Repository 错误
	}

	// 检查标题是否与其他建议冲突
	if req.Title != "" && req.Title != advice.Title {
		exists, err := n.nutritionAdviceRepo.ExistsByTitleExcludeID(req.Title, id)
		if err != nil {
			return nil, err // 透传 Repository 错误
		}
		if exists {
			return nil, errs.New(errs.CodeNutritionAlreadyExists, "nutrition advice title already exists")
		}
	}

	// 更新字段
	if req.Type != "" {
		advice.Type = req.Type
	}
	if req.Title != "" {
		advice.UpdateContent(req.Title, advice.Description)
	}
	if req.Description != "" {
		advice.UpdateContent(advice.Title, req.Description)
	}
	if req.ConditionType != "" {
		advice.ConditionType = req.ConditionType
	}
	if req.MinPercentage != nil || req.MaxPercentage != nil {
		advice.SetPercentageRange(req.MinPercentage, req.MaxPercentage)
	}
	if req.IsDefault != nil {
		advice.SetAsDefault(*req.IsDefault)
	}
	if req.Priority != nil {
		advice.UpdatePriority(*req.Priority)
	}
	if req.Status != nil {
		advice.SetStatus(*req.Status)
	}

	// 保存更新
	if err := n.nutritionAdviceRepo.Update(advice); err != nil {
		return nil, err // 透传 Repository 错误
	}

	return n.convertToResponse(advice), nil
}

// DeleteNutritionAdvice 删除营养建议
func (n *nutritionAdviceLogic) DeleteNutritionAdvice(ctx context.Context, id int64) error {
	if id <= 0 {
		return errs.New(errs.CodeNutritionInvalidParams, "id must be positive")
	}

	err := n.nutritionAdviceRepo.Delete(id)
	if err != nil {
		var notFound *repositories.NotFoundError
		if errors.As(err, &notFound) {
			return errs.New(errs.CodeNutritionNotFound, "nutrition advice not found")
		}
		return err // 透传 Repository 错误
	}

	return nil
}

// GetNutritionAdviceByID 根据ID获取营养建议
func (n *nutritionAdviceLogic) GetNutritionAdviceByID(ctx context.Context, id int64) (*v1.NutritionAdviceResponse, error) {
	if id <= 0 {
		return nil, errs.New(errs.CodeNutritionInvalidParams, "id must be positive")
	}

	advice, err := n.nutritionAdviceRepo.GetByID(id)
	if err != nil {
		var notFound *repositories.NotFoundError
		if errors.As(err, &notFound) {
			return nil, errs.New(errs.CodeNutritionNotFound, "nutrition advice not found")
		}
		return nil, err // 透传 Repository 错误
	}

	return n.convertToResponse(advice), nil
}

// ListNutritionAdvice 获取营养建议列表（分页）
func (n *nutritionAdviceLogic) ListNutritionAdvice(ctx context.Context, req *v1.NutritionAdviceQueryReq) (*v1.NutritionAdviceListResponse, error) {
	if req == nil {
		return nil, errs.New(errs.CodeNutritionInvalidParams, "request is required")
	}

	// 设置默认分页参数
	current := req.Current
	if current <= 0 {
		current = 1
	}
	size := req.Size
	if size <= 0 {
		size = 10
	}

	offset := (current - 1) * size

	var advices []*entities.NutritionAdvice
	var total int64
	var err error

	// 根据查询条件获取数据
	if req.ConditionType != nil && req.Status != nil {
		// 同时有条件类型和状态筛选
		advices, total, err = n.nutritionAdviceRepo.FindByConditionTypeAndStatus(*req.ConditionType, *req.Status, offset, size)
	} else if req.ConditionType != nil {
		// 只有条件类型筛选
		advices, total, err = n.nutritionAdviceRepo.FindByConditionTypeWithPagination(*req.ConditionType, offset, size)
	} else if req.Status != nil {
		// 只有状态筛选
		advices, total, err = n.nutritionAdviceRepo.FindByStatus(*req.Status, offset, size)
	} else {
		// 无筛选条件
		advices, total, err = n.nutritionAdviceRepo.FindAll(offset, size)
	}

	if err != nil {
		return nil, err // 透传 Repository 错误
	}

	// 转换为响应格式
	records := make([]*v1.NutritionAdviceResponse, len(advices))
	for i, advice := range advices {
		records[i] = n.convertToResponse(advice)
	}

	return &v1.NutritionAdviceListResponse{
		Total:   total,
		Records: records,
		Current: current,
		Size:    size,
	}, nil
}

// GetAdvicesByConditionType 根据条件类型获取所有启用的营养建议
func (n *nutritionAdviceLogic) GetAdvicesByConditionType(ctx context.Context, conditionType string) ([]*v1.NutritionAdviceResponse, error) {
	if conditionType == "" {
		return nil, errs.New(errs.CodeNutritionInvalidParams, "conditionType is required")
	}

	advices, err := n.nutritionAdviceRepo.FindByConditionType(conditionType)
	if err != nil {
		return nil, err // 透传 Repository 错误
	}

	responses := make([]*v1.NutritionAdviceResponse, len(advices))
	for i, advice := range advices {
		responses[i] = n.convertToResponse(advice)
	}

	return responses, nil
}

// UpdateAdviceStatus 更新营养建议状态
func (n *nutritionAdviceLogic) UpdateAdviceStatus(ctx context.Context, id int64, req *v1.NutritionAdviceStatusUpdateReq) error {
	if id <= 0 {
		return errs.New(errs.CodeNutritionInvalidParams, "id must be positive")
	}
	if req == nil {
		return errs.New(errs.CodeNutritionInvalidParams, "request is required")
	}

	err := n.nutritionAdviceRepo.UpdateStatus(id, req.Status)
	if err != nil {
		var notFound *repositories.NotFoundError
		if errors.As(err, &notFound) {
			return errs.New(errs.CodeNutritionNotFound, "nutrition advice not found")
		}
		return err // 透传 Repository 错误
	}

	return nil
}

// convertToResponse 将实体转换为响应DTO
func (n *nutritionAdviceLogic) convertToResponse(advice *entities.NutritionAdvice) *v1.NutritionAdviceResponse {
	if advice == nil {
		return nil
	}

	return &v1.NutritionAdviceResponse{
		ID:            advice.ID,
		Type:          advice.Type,
		Title:         advice.Title,
		Description:   advice.Description,
		ConditionType: advice.ConditionType,
		MinPercentage: advice.MinPercentage,
		MaxPercentage: advice.MaxPercentage,
		IsDefault:     advice.IsDefault,
		Priority:      advice.Priority,
		Status:        advice.Status,
		CreatedAt:     advice.CreatedAt,
		UpdatedAt:     advice.UpdatedAt,
	}
}

// convertToDisplayResponse 将响应DTO转换为显示DTO
func (n *nutritionAdviceLogic) convertToDisplayResponse(advice *v1.NutritionAdviceResponse) *v1.NutritionAdviceDisplayResponse {
	if advice == nil {
		return nil
	}

	return &v1.NutritionAdviceDisplayResponse{
		Type:        advice.Type,
		Title:       advice.Title,
		Description: advice.Description,
	}
}
